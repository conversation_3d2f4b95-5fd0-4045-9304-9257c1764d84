import json
import numpy as np

# Load sample features
features_file = "data/processed/phase6_5/advanced_features/!!!Chaos Time!!!_features.json"
matrix_file = "data/processed/phase6_5/feature_matrices/!!!Chaos Time!!!_features.npy"

with open(features_file, 'r') as f:
    features = json.load(f)

matrix = np.load(matrix_file)

print(f"Total features: {len(features)}")
print(f"Feature vector size: {len(features[0]['feature_vector'])}")
print(f"Sample quality metrics:")
print(f"  Overall quality: {features[0]['quality_metrics']['overall_feature_quality']:.3f}")
print(f"  Phase 6 data quality: {features[0]['quality_metrics']['phase6_data_quality']:.3f}")
print(f"  High quality: {features[0]['quality_metrics']['is_high_quality']}")
print(f"Matrix shape: {matrix.shape}")
print(f"Non-zero features: {np.count_nonzero(matrix[0])}/128")
print(f"Feature range: [{np.min(matrix):.3f}, {np.max(matrix):.3f}]")

# Check feature categories
sample_feature = features[0]
print(f"\nFeature categories available:")
for key in sample_feature.keys():
    if key != 'feature_vector':
        print(f"  - {key}")

print(f"\nSpectral features keys: {list(sample_feature['spectral_features'].keys())}")
print(f"Rhythmic features keys: {list(sample_feature['rhythmic_features'].keys())}")
print(f"Harmonic features keys: {list(sample_feature['harmonic_features'].keys())}")
