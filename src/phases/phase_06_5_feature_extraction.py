"""
Phase 6.5: Advanced Feature Extraction

This phase extracts comprehensive machine learning features from audio candidates
to prepare high-quality inputs for the classification models.

Features:
- Reliable spectral feature extraction using numpy/scipy
- Simple rhythmic features without problematic librosa functions
- Energy-based features and statistical analysis
- Sequential processing for maximum stability
- 128-dimensional feature vectors for Phase 7 compatibility

Author: TJAGen Pipeline
Version: 1.0.0
"""

import json
import logging
import time
import gc
import sys
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union
import numpy as np
import warnings
from tqdm import tqdm
import traceback

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent))

# Import utilities
from utils.audio_utils import MemoryManager
from utils.path_utils import create_output_directories

# Suppress warnings
warnings.filterwarnings("ignore", category=UserWarning, module="librosa")
warnings.filterwarnings("ignore", category=FutureWarning)

logger = logging.getLogger(__name__)


class AdvancedFeatureExtractor:
    """
    Advanced feature extractor for TJA chart generation pipeline.

    Features:
    - Reliable spectral features using numpy/scipy
    - Simple rhythmic features without problematic functions
    - Energy-based features and statistical analysis
    - Sequential processing for maximum stability
    - 128-dimensional feature vectors
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """Initialize advanced feature extractor."""
        self.config = config or self._get_default_config()
        logger.info("Advanced Feature Extractor initialized")
    
    def _get_default_config(self) -> Dict:
        """Get default configuration for optimized feature extraction."""
        return {
            "feature_vector_size": 128,
            "sample_rate": 22050,
            "min_audio_length": 1024,
            "mel_bands": 13,  # Standard MFCC bands
            "spectral_bands": 12,  # Chromatic scale-based bands
            "rhythm_features": 8,  # Rhythm pattern analysis
            "harmonic_features": 6,  # Harmonic content analysis
            "temporal_features": 10,  # Time-domain characteristics
            "contextual_features": 5   # Beat/onset context features
        }
    
    def extract_features(self, candidates: List[Dict], audio_data: np.ndarray) -> List[Dict]:
        """
        Extract advanced features from candidates.

        Args:
            candidates: List of candidate dictionaries
            audio_data: Full audio data

        Returns:
            List of feature dictionaries
        """
        # Input validation
        if not candidates:
            logger.warning("No candidates provided for feature extraction")
            return []

        if audio_data is None or len(audio_data) == 0:
            logger.error("Invalid or empty audio data provided")
            return [self._create_fallback_features(candidate) for candidate in candidates]

        # Validate audio data
        if not isinstance(audio_data, np.ndarray):
            logger.error("Audio data must be a numpy array")
            return [self._create_fallback_features(candidate) for candidate in candidates]

        if np.any(np.isnan(audio_data)) or np.any(np.isinf(audio_data)):
            logger.warning("Audio data contains NaN or infinite values, cleaning...")
            audio_data = np.nan_to_num(audio_data, nan=0.0, posinf=0.0, neginf=0.0)

        logger.info(f"Extracting features for {len(candidates)} candidates from {len(audio_data)} audio samples...")

        features = []
        
        for i, candidate in enumerate(tqdm(candidates, desc="Extracting features")):
            try:
                # Extract audio snippet
                snippet = self._extract_audio_snippet(candidate, audio_data)
                
                # Extract simple features
                feature_dict = self._extract_simple_features(candidate, snippet)
                features.append(feature_dict)
                
                # Progress logging
                if (i + 1) % 50 == 0:
                    logger.debug(f"Processed {i + 1}/{len(candidates)} candidates")
                
            except Exception as e:
                logger.warning(f"Failed to extract features for candidate {candidate.get('window_id', i)}: {e}")
                # Add fallback features
                features.append(self._create_fallback_features(candidate))
        
        logger.info(f"Feature extraction completed: {len(features)} features extracted")
        return features
    
    def _extract_audio_snippet(self, candidate: Dict, audio_data: np.ndarray) -> np.ndarray:
        """Extract audio snippet for candidate."""
        try:
            start_time = candidate.get('start_time', 0.0)
            end_time = candidate.get('end_time', 0.1)
            
            # Convert to samples
            start_sample = int(start_time * self.config["sample_rate"])
            end_sample = int(end_time * self.config["sample_rate"])
            
            # Ensure valid range
            start_sample = max(0, start_sample)
            end_sample = min(len(audio_data), end_sample)
            
            if end_sample > start_sample:
                snippet = audio_data[start_sample:end_sample]
            else:
                snippet = np.zeros(self.config["min_audio_length"])
            
            # Ensure minimum length
            if len(snippet) < self.config["min_audio_length"]:
                snippet = np.pad(snippet, (0, self.config["min_audio_length"] - len(snippet)))
            
            return snippet.astype(np.float32)
            
        except Exception as e:
            logger.debug(f"Audio snippet extraction failed: {e}")
            return np.zeros(self.config["min_audio_length"], dtype=np.float32)
    
    def _extract_simple_features(self, candidate: Dict, audio_snippet: np.ndarray) -> Dict:
        """Extract optimized features leveraging Phase 6 candidate data."""
        try:
            # Extract Phase 6 pre-computed features
            phase6_features = self._extract_phase6_features(candidate)

            # Extract enhanced audio features
            audio_features = self._extract_enhanced_audio_features(audio_snippet)

            # Extract contextual features from candidate metadata
            contextual_features = self._extract_contextual_features(candidate)

            # Create optimized feature vector
            feature_vector = self._create_optimized_feature_vector(
                phase6_features, audio_features, contextual_features, audio_snippet
            )
            
            return {
                "candidate_id": candidate.get('window_id', 'unknown'),
                "window_id": candidate.get('window_id', 'unknown'),
                "feature_vector": feature_vector,
                "spectral_features": {
                    "rms_energy": audio_features['rms_energy'],
                    "max_amplitude": audio_features['max_amplitude'],
                    "zero_crossing_rate": phase6_features['zero_crossing_rate'],
                    "spectral_centroid": phase6_features['spectral_centroid'],
                    "spectral_rolloff": phase6_features['spectral_rolloff'],
                    "spectral_bandwidth": phase6_features['spectral_bandwidth'],
                    "mfcc_mean": phase6_features['mfcc_mean'].tolist(),
                    "chromatic_features": audio_features['chromatic_features'].tolist()
                },
                "rhythmic_features": {
                    "rhythm_pattern": audio_features['rhythm_features'].tolist(),
                    "onset_strength": phase6_features['onset_strength'],
                    "beat_position": contextual_features['beat_position'],
                    "subdivision": contextual_features['subdivision_value']
                },
                "harmonic_features": {
                    "harmonic_content": audio_features['harmonic_features'].tolist(),
                    "fundamental_freq": phase6_features['spectral_centroid'],  # Approximation
                    "harmonic_ratio": audio_features['harmonic_features'][4] if len(audio_features['harmonic_features']) > 4 else 0.5,
                    "spectral_flatness": audio_features['harmonic_features'][5] if len(audio_features['harmonic_features']) > 5 else 0.5
                },
                "energy_features": {
                    "rms_energy": audio_features['rms_energy'],
                    "max_amplitude": audio_features['max_amplitude'],
                    "dynamic_range": self._calculate_dynamic_range_safe(audio_features['max_amplitude'], audio_features['rms_energy']),
                    "energy_profile": phase6_features['energy_profile'].tolist(),
                    "temporal_envelope": audio_features['temporal_features'].tolist()
                },
                "contextual_features": {
                    "beat_position": contextual_features['beat_position'],
                    "duration": contextual_features['duration'],
                    "center_time": contextual_features['center_time'],
                    "subdivision_value": contextual_features['subdivision_value'],
                    "confidence": contextual_features['confidence'],
                    "type_value": contextual_features['type_value']
                },
                "quality_metrics": {
                    "feature_completeness": 1.0,
                    "extraction_confidence": contextual_features['confidence'],
                    "phase6_data_quality": 1.0 if np.any(phase6_features['mfcc_mean'] != 0) else 0.5,
                    "audio_quality": min(audio_features['rms_energy'] * 10, 1.0),
                    "overall_feature_quality": (contextual_features['confidence'] + contextual_features['type_value']) / 2,
                    "is_high_quality": contextual_features['confidence'] > 0.6
                }
            }
            
        except Exception as e:
            logger.warning(f"Simple feature extraction failed: {e}")
            return self._create_fallback_features(candidate)
    
    def _estimate_tempo_simple(self, audio_snippet: np.ndarray) -> float:
        """Simple tempo estimation without librosa."""
        try:
            # Simple autocorrelation-based tempo estimation
            # This is a very basic implementation
            
            # Calculate energy envelope
            hop_length = 512
            frame_length = 1024
            
            if len(audio_snippet) < frame_length:
                return 120.0  # Default tempo
            
            # Simple frame-based energy
            n_frames = (len(audio_snippet) - frame_length) // hop_length + 1
            energy = np.zeros(n_frames)
            
            for i in range(n_frames):
                start = i * hop_length
                end = start + frame_length
                frame = audio_snippet[start:end]
                energy[i] = np.sum(frame ** 2)
            
            # Simple peak detection for tempo
            if len(energy) < 10:
                return 120.0
            
            # Find peaks in energy
            peaks = []
            for i in range(1, len(energy) - 1):
                if energy[i] > energy[i-1] and energy[i] > energy[i+1]:
                    peaks.append(i)
            
            if len(peaks) < 2:
                return 120.0
            
            # Estimate tempo from peak intervals
            intervals = np.diff(peaks) * hop_length / self.config["sample_rate"]
            if len(intervals) > 0:
                avg_interval = np.median(intervals)
                tempo = 60.0 / avg_interval if avg_interval > 0 else 120.0
                # Clamp to reasonable range
                tempo = max(60.0, min(200.0, tempo))
            else:
                tempo = 120.0
            
            return float(tempo)
            
        except Exception as e:
            logger.debug(f"Simple tempo estimation failed: {e}")
            return 120.0

    def _extract_phase6_features(self, candidate: Dict) -> Dict:
        """Extract and process pre-computed Phase 6 features."""
        try:
            spectral_features = candidate.get('spectral_features', {})

            # Extract MFCC features (13 coefficients)
            mfcc_mean = spectral_features.get('mfcc_mean', [0.0] * 13)
            if len(mfcc_mean) != 13:
                mfcc_mean = (mfcc_mean + [0.0] * 13)[:13]  # Pad or truncate to 13

            # Extract spectral characteristics
            spectral_centroid = spectral_features.get('spectral_centroid', 2000.0)
            spectral_rolloff = spectral_features.get('spectral_rolloff', 4000.0)
            spectral_bandwidth = spectral_features.get('spectral_bandwidth', 1500.0)
            zero_crossing_rate = spectral_features.get('zero_crossing_rate', 0.1)

            # Extract onset and energy information
            onset_strength = candidate.get('onset_strength', 1.0)
            energy_profile = candidate.get('energy_profile', [])

            return {
                'mfcc_mean': np.array(mfcc_mean, dtype=np.float32),
                'spectral_centroid': float(spectral_centroid),
                'spectral_rolloff': float(spectral_rolloff),
                'spectral_bandwidth': float(spectral_bandwidth),
                'zero_crossing_rate': float(zero_crossing_rate),
                'onset_strength': float(onset_strength),
                'energy_profile': np.array(energy_profile, dtype=np.float32) if energy_profile else np.zeros(13, dtype=np.float32)
            }

        except Exception as e:
            logger.debug(f"Phase 6 feature extraction failed: {e}")
            return {
                'mfcc_mean': np.zeros(13, dtype=np.float32),
                'spectral_centroid': 2000.0,
                'spectral_rolloff': 4000.0,
                'spectral_bandwidth': 1500.0,
                'zero_crossing_rate': 0.1,
                'onset_strength': 1.0,
                'energy_profile': np.zeros(13, dtype=np.float32)
            }

    def _extract_enhanced_audio_features(self, audio_snippet: np.ndarray) -> Dict:
        """Extract enhanced audio features using reliable methods."""
        try:
            # Basic energy features
            rms_energy = float(np.sqrt(np.mean(audio_snippet ** 2)))
            max_amplitude = float(np.max(np.abs(audio_snippet)))

            # Enhanced spectral analysis
            fft = np.fft.rfft(audio_snippet)
            magnitude = np.abs(fft)
            freqs = np.fft.rfftfreq(len(audio_snippet), 1/self.config["sample_rate"])

            # Chromatic spectral features (12 bands based on musical notes)
            chromatic_features = self._extract_chromatic_features(magnitude, freqs)

            # Harmonic content analysis
            harmonic_features = self._extract_harmonic_content(magnitude, freqs)

            # Temporal envelope features
            temporal_features = self._extract_temporal_envelope(audio_snippet)

            # Rhythm pattern analysis
            rhythm_features = self._extract_rhythm_pattern(audio_snippet)

            return {
                'rms_energy': rms_energy,
                'max_amplitude': max_amplitude,
                'chromatic_features': chromatic_features,
                'harmonic_features': harmonic_features,
                'temporal_features': temporal_features,
                'rhythm_features': rhythm_features
            }

        except Exception as e:
            logger.debug(f"Enhanced audio feature extraction failed: {e}")
            return {
                'rms_energy': 0.0,
                'max_amplitude': 0.0,
                'chromatic_features': np.zeros(12, dtype=np.float32),
                'harmonic_features': np.zeros(6, dtype=np.float32),
                'temporal_features': np.zeros(10, dtype=np.float32),
                'rhythm_features': np.zeros(8, dtype=np.float32)
            }

    def _extract_contextual_features(self, candidate: Dict) -> Dict:
        """Extract contextual features from candidate metadata."""
        try:
            # Beat and timing context
            beat_position = candidate.get('beat_position', 0.0)
            duration = candidate.get('duration', 0.15)
            center_time = candidate.get('center_time', 0.0)

            # Beat subdivision context
            beat_subdivision = candidate.get('beat_subdivision', 'quarter')
            subdivision_encoding = {
                'sixteenth': 0.25, 'eighth': 0.5, 'quarter': 1.0,
                'half': 2.0, 'whole': 4.0
            }
            subdivision_value = subdivision_encoding.get(beat_subdivision, 1.0)

            # Candidate confidence and type
            confidence = candidate.get('candidate_confidence', 0.5)
            candidate_type = candidate.get('candidate_type', 'weak')
            type_encoding = {'weak': 0.3, 'medium': 0.6, 'strong': 1.0}
            type_value = type_encoding.get(candidate_type, 0.5)

            return {
                'beat_position': float(beat_position),
                'duration': float(duration),
                'center_time': float(center_time),
                'subdivision_value': float(subdivision_value),
                'confidence': float(confidence),
                'type_value': float(type_value)
            }

        except Exception as e:
            logger.debug(f"Contextual feature extraction failed: {e}")
            return {
                'beat_position': 0.0,
                'duration': 0.15,
                'center_time': 0.0,
                'subdivision_value': 1.0,
                'confidence': 0.5,
                'type_value': 0.5
            }

    def _calculate_dynamic_range_safe(self, max_amplitude: float, rms_energy: float) -> float:
        """Calculate dynamic range safely without divide-by-zero warnings."""
        try:
            # Ensure both values are positive and non-zero
            max_amp_safe = max(abs(max_amplitude), 1e-10)
            rms_safe = max(abs(rms_energy), 1e-10)

            # Calculate ratio with bounds checking
            if max_amp_safe <= rms_safe:
                return 0.0  # No dynamic range

            ratio = max_amp_safe / rms_safe
            dynamic_range = 20 * np.log10(ratio)

            # Clamp to reasonable range
            return float(np.clip(dynamic_range, 0.0, 120.0))

        except Exception as e:
            logger.debug(f"Dynamic range calculation failed: {e}")
            return 20.0  # Default reasonable value

    def _extract_chromatic_features(self, magnitude: np.ndarray, freqs: np.ndarray) -> np.ndarray:
        """Extract chromatic features based on musical note frequencies."""
        try:
            # Define chromatic scale frequencies (C, C#, D, D#, E, F, F#, G, G#, A, A#, B)
            # Using A4 = 440Hz as reference
            chromatic_freqs = np.array([
                261.63, 277.18, 293.66, 311.13, 329.63, 349.23,  # C4-F4
                369.99, 392.00, 415.30, 440.00, 466.16, 493.88   # F#4-B4
            ])

            chromatic_features = np.zeros(12, dtype=np.float32)

            for i, target_freq in enumerate(chromatic_freqs):
                # Find frequency bins around each chromatic note (±10% tolerance)
                freq_tolerance = target_freq * 0.1
                freq_mask = (freqs >= target_freq - freq_tolerance) & (freqs <= target_freq + freq_tolerance)

                if np.any(freq_mask):
                    chromatic_features[i] = np.sum(magnitude[freq_mask])

            # Normalize by total magnitude
            total_magnitude = np.sum(magnitude) + 1e-10
            chromatic_features = chromatic_features / total_magnitude

            return chromatic_features

        except Exception as e:
            logger.debug(f"Chromatic feature extraction failed: {e}")
            return np.zeros(12, dtype=np.float32)

    def _extract_harmonic_content(self, magnitude: np.ndarray, freqs: np.ndarray) -> np.ndarray:
        """Extract harmonic content features."""
        try:
            harmonic_features = np.zeros(6, dtype=np.float32)

            # Find fundamental frequency (strongest peak)
            if len(magnitude) > 0:
                fundamental_idx = np.argmax(magnitude)
                fundamental_freq = freqs[fundamental_idx] if fundamental_idx < len(freqs) else 440.0

                # Analyze harmonics (2f, 3f, 4f, 5f)
                for harmonic in range(2, 6):
                    target_freq = fundamental_freq * harmonic
                    if target_freq < freqs[-1]:  # Within Nyquist limit
                        # Find closest frequency bin
                        freq_idx = np.argmin(np.abs(freqs - target_freq))
                        harmonic_features[harmonic-2] = magnitude[freq_idx]

                # Harmonic-to-noise ratio
                harmonic_energy = np.sum(harmonic_features)
                total_energy = np.sum(magnitude) + 1e-10
                harmonic_features[4] = harmonic_energy / total_energy

                # Spectral flatness (measure of noisiness)
                geometric_mean = np.exp(np.mean(np.log(magnitude + 1e-10)))
                arithmetic_mean = np.mean(magnitude) + 1e-10
                harmonic_features[5] = geometric_mean / arithmetic_mean

            return harmonic_features

        except Exception as e:
            logger.debug(f"Harmonic content extraction failed: {e}")
            return np.zeros(6, dtype=np.float32)

    def _extract_temporal_envelope(self, audio_snippet: np.ndarray) -> np.ndarray:
        """Extract temporal envelope features."""
        try:
            temporal_features = np.zeros(10, dtype=np.float32)

            # Envelope using Hilbert transform
            analytic_signal = np.abs(np.fft.hilbert(audio_snippet))

            # Envelope statistics
            temporal_features[0] = np.mean(analytic_signal)
            temporal_features[1] = np.std(analytic_signal)
            temporal_features[2] = np.max(analytic_signal)
            temporal_features[3] = np.min(analytic_signal)

            # Attack and decay characteristics
            peak_idx = np.argmax(analytic_signal)
            if peak_idx > 0:
                # Attack time (time to reach peak)
                temporal_features[4] = peak_idx / len(analytic_signal)
                # Attack slope
                attack_segment = analytic_signal[:peak_idx+1]
                if len(attack_segment) > 1:
                    temporal_features[5] = (attack_segment[-1] - attack_segment[0]) / len(attack_segment)

            if peak_idx < len(analytic_signal) - 1:
                # Decay time (time from peak to end)
                temporal_features[6] = (len(analytic_signal) - peak_idx) / len(analytic_signal)
                # Decay slope
                decay_segment = analytic_signal[peak_idx:]
                if len(decay_segment) > 1:
                    temporal_features[7] = (decay_segment[-1] - decay_segment[0]) / len(decay_segment)

            # Envelope modulation (variation over time)
            if len(analytic_signal) > 10:
                # Divide into segments and measure variation
                segment_size = len(analytic_signal) // 5
                segment_means = []
                for i in range(5):
                    start = i * segment_size
                    end = start + segment_size if i < 4 else len(analytic_signal)
                    segment_means.append(np.mean(analytic_signal[start:end]))
                temporal_features[8] = np.std(segment_means)
                temporal_features[9] = np.max(segment_means) - np.min(segment_means)

            return temporal_features

        except Exception as e:
            logger.debug(f"Temporal envelope extraction failed: {e}")
            return np.zeros(10, dtype=np.float32)

    def _extract_rhythm_pattern(self, audio_snippet: np.ndarray) -> np.ndarray:
        """Extract rhythm pattern features using onset detection."""
        try:
            rhythm_features = np.zeros(8, dtype=np.float32)

            # Simple onset detection using energy differences
            hop_length = 256
            frame_length = 512

            if len(audio_snippet) < frame_length:
                return rhythm_features

            # Calculate frame-wise energy
            n_frames = (len(audio_snippet) - frame_length) // hop_length + 1
            frame_energy = np.zeros(n_frames)

            for i in range(n_frames):
                start = i * hop_length
                end = start + frame_length
                frame = audio_snippet[start:end]
                frame_energy[i] = np.sum(frame ** 2)

            if len(frame_energy) < 3:
                return rhythm_features

            # Onset detection using energy differences
            energy_diff = np.diff(frame_energy)
            positive_diffs = energy_diff[energy_diff > 0]

            # Rhythm features
            rhythm_features[0] = np.mean(positive_diffs) if len(positive_diffs) > 0 else 0.0
            rhythm_features[1] = np.std(positive_diffs) if len(positive_diffs) > 0 else 0.0
            rhythm_features[2] = len(positive_diffs) / len(energy_diff)  # Onset density

            # Regularity measures
            if len(positive_diffs) > 1:
                onset_intervals = np.diff(np.where(energy_diff > 0)[0])
                if len(onset_intervals) > 0:
                    rhythm_features[3] = np.mean(onset_intervals)
                    rhythm_features[4] = np.std(onset_intervals)
                    rhythm_features[5] = np.max(onset_intervals) - np.min(onset_intervals)

            # Energy envelope periodicity
            if len(frame_energy) > 8:
                # Simple autocorrelation for periodicity
                autocorr = np.correlate(frame_energy, frame_energy, mode='full')
                autocorr = autocorr[len(autocorr)//2:]
                if len(autocorr) > 4:
                    rhythm_features[6] = np.max(autocorr[1:5]) / (autocorr[0] + 1e-10)
                    rhythm_features[7] = np.argmax(autocorr[1:5]) + 1

            return rhythm_features

        except Exception as e:
            logger.debug(f"Rhythm pattern extraction failed: {e}")
            return np.zeros(8, dtype=np.float32)

    def _create_optimized_feature_vector(self, phase6_features: Dict, audio_features: Dict,
                                       contextual_features: Dict, audio_snippet: np.ndarray) -> np.ndarray:
        """Create optimized 128-dimensional feature vector leveraging all available data."""
        try:
            # Pre-allocate array for better performance
            feature_vector = np.zeros(self.config["feature_vector_size"], dtype=np.float32)
            idx = 0

            # 1. MFCC features from Phase 6 (13 dimensions)
            mfcc_features = phase6_features['mfcc_mean']
            feature_vector[idx:idx+13] = mfcc_features
            idx += 13

            # 2. Enhanced spectral features (8 dimensions)
            spectral_features = [
                min(phase6_features['spectral_centroid'] / 10000.0, 1.0),
                min(phase6_features['spectral_rolloff'] / 10000.0, 1.0),
                min(phase6_features['spectral_bandwidth'] / 5000.0, 1.0),
                phase6_features['zero_crossing_rate'],
                min(phase6_features['onset_strength'] / 10.0, 1.0),
                audio_features['rms_energy'],
                audio_features['max_amplitude'],
                self._calculate_dynamic_range_safe(audio_features['max_amplitude'], audio_features['rms_energy']) / 60.0
            ]
            feature_vector[idx:idx+8] = spectral_features
            idx += 8

            # 3. Chromatic features (12 dimensions) - Musical note content
            feature_vector[idx:idx+12] = audio_features['chromatic_features']
            idx += 12

            # 4. Harmonic content features (6 dimensions)
            feature_vector[idx:idx+6] = audio_features['harmonic_features']
            idx += 6

            # 5. Temporal envelope features (10 dimensions)
            feature_vector[idx:idx+10] = audio_features['temporal_features']
            idx += 10

            # 6. Rhythm pattern features (8 dimensions)
            feature_vector[idx:idx+8] = audio_features['rhythm_features']
            idx += 8

            # 7. Contextual features (6 dimensions) - Beat/timing context
            contextual_array = [
                contextual_features['beat_position'],
                min(contextual_features['duration'] / 0.5, 1.0),  # Normalize duration
                contextual_features['subdivision_value'] / 4.0,   # Normalize subdivision
                contextual_features['confidence'],
                contextual_features['type_value'],
                min(contextual_features['center_time'] / 300.0, 1.0)  # Normalize time (5 min max)
            ]
            feature_vector[idx:idx+6] = contextual_array
            idx += 6

            # 8. Energy profile features (13 dimensions) - From Phase 6
            energy_profile = phase6_features['energy_profile']
            if len(energy_profile) >= 13:
                feature_vector[idx:idx+13] = energy_profile[:13]
            else:
                # Pad if necessary
                padded_profile = np.zeros(13, dtype=np.float32)
                padded_profile[:len(energy_profile)] = energy_profile
                feature_vector[idx:idx+13] = padded_profile
            idx += 13

            # 9. Advanced spectral distribution (remaining dimensions)
            remaining_dims = self.config["feature_vector_size"] - idx

            if remaining_dims > 0:
                # Use remaining dimensions for advanced spectral analysis
                fft = np.fft.rfft(audio_snippet)
                magnitude = np.abs(fft)

                if len(magnitude) > 0:
                    # Logarithmic frequency bands for better musical representation
                    freqs = np.fft.rfftfreq(len(audio_snippet), 1/self.config["sample_rate"])

                    # Create logarithmically spaced frequency bands
                    min_freq = max(freqs[1], 80.0)  # Avoid DC and very low frequencies
                    max_freq = min(freqs[-1], 8000.0)  # Focus on musical range

                    if max_freq > min_freq:
                        log_bands = np.logspace(np.log10(min_freq), np.log10(max_freq), remaining_dims)

                        spectral_distribution = np.zeros(remaining_dims, dtype=np.float32)
                        total_magnitude = np.sum(magnitude) + 1e-10

                        for i in range(remaining_dims - 1):
                            # Find frequency bins in this logarithmic band
                            band_mask = (freqs >= log_bands[i]) & (freqs < log_bands[i + 1])
                            if np.any(band_mask):
                                spectral_distribution[i] = np.sum(magnitude[band_mask]) / total_magnitude

                        # Last band gets remaining high frequencies
                        band_mask = freqs >= log_bands[-1]
                        if np.any(band_mask):
                            spectral_distribution[-1] = np.sum(magnitude[band_mask]) / total_magnitude

                        feature_vector[idx:idx+remaining_dims] = spectral_distribution
                    else:
                        # Fallback: use uniform distribution
                        feature_vector[idx:idx+remaining_dims] = magnitude[:remaining_dims] / (np.sum(magnitude) + 1e-10)
                else:
                    # No magnitude data available
                    feature_vector[idx:idx+remaining_dims] = 0.0

            # Final validation and normalization
            feature_vector = np.nan_to_num(feature_vector, nan=0.0, posinf=1.0, neginf=-1.0)
            feature_vector = np.clip(feature_vector, -5.0, 5.0)  # Reasonable bounds for ML

            return feature_vector
            
        except Exception as e:
            logger.warning(f"Feature vector creation failed: {e}")
            return np.zeros(self.config["feature_vector_size"], dtype=np.float32)
    
    def _create_fallback_features(self, candidate: Dict) -> Dict:
        """Create fallback features for failed extractions with enhanced structure."""
        return {
            "candidate_id": candidate.get('window_id', 'unknown'),
            "window_id": candidate.get('window_id', 'unknown'),
            "feature_vector": np.zeros(self.config["feature_vector_size"], dtype=np.float32),
            "spectral_features": {
                "rms_energy": 0.0,
                "max_amplitude": 0.0,
                "zero_crossing_rate": 0.0,
                "spectral_centroid": 2000.0,  # Reasonable default
                "spectral_rolloff": 4000.0,   # Reasonable default
                "spectral_bandwidth": 1500.0, # Reasonable default
                "mfcc_mean": [0.0] * 13,
                "chromatic_features": [0.0] * 12
            },
            "rhythmic_features": {
                "rhythm_pattern": [0.0] * 8,
                "onset_strength": 0.0,
                "beat_position": 0.0,
                "subdivision": 1.0
            },
            "harmonic_features": {
                "harmonic_content": [0.0] * 6,
                "fundamental_freq": 440.0,
                "harmonic_ratio": 0.0,
                "spectral_flatness": 1.0  # Maximum flatness (noise-like)
            },
            "energy_features": {
                "rms_energy": 0.0,
                "max_amplitude": 0.0,
                "dynamic_range": 0.0,
                "energy_profile": [0.0] * 13,
                "temporal_envelope": [0.0] * 10
            },
            "contextual_features": {
                "beat_position": 0.0,
                "duration": 0.15,  # Default duration
                "center_time": 0.0,
                "subdivision_value": 1.0,
                "confidence": 0.0,
                "type_value": 0.3  # Weak candidate
            },
            "quality_metrics": {
                "feature_completeness": 0.0,
                "extraction_confidence": 0.0,
                "phase6_data_quality": 0.0,
                "audio_quality": 0.0,
                "overall_feature_quality": 0.0,
                "is_high_quality": False
            }
        }


def run_phase_06_5(input_dirs: Optional[Dict[str, str]] = None,
                  output_dir: str = "data/processed/phase6_5",
                  song_list: Optional[List[str]] = None,
                  config: Optional[Dict] = None) -> Dict:
    """
    Run Phase 6.5 advanced feature extraction processing.

    Args:
        input_dirs: Dictionary of input directories for each phase
        output_dir: Directory for Phase 6.5 outputs
        song_list: Optional list of specific songs to process
        config: Optional configuration dictionary

    Returns:
        Processing statistics dictionary
    """
    # Setup default input directories
    if input_dirs is None:
        input_dirs = {
            "phase1": "data/processed/phase1",
            "phase6": "data/processed/phase6"
        }

    # Convert to Path objects
    input_paths = {k: Path(v) for k, v in input_dirs.items()}
    output_path = Path(output_dir)

    logger.info("Starting Phase 6.5 Advanced Feature Extraction")
    logger.info(f"Input directories: {input_dirs}")
    logger.info(f"Output directory: {output_dir}")

    try:
        # Create output directories
        create_output_directories(output_path, [
            "advanced_features",
            "feature_matrices",
            "extraction_stats"
        ])

        # Initialize feature extractor
        extractor = AdvancedFeatureExtractor(config)

        # Find songs to process
        if song_list is None:
            candidates_dir = input_paths["phase6"] / "note_candidates"
            if not candidates_dir.exists():
                raise FileNotFoundError(f"Candidates directory not found: {candidates_dir}")

            candidate_files = list(candidates_dir.glob("*_candidates.json"))
            song_list = [f.stem.replace("_candidates", "") for f in candidate_files]

        if not song_list:
            raise ValueError("No songs found to process")

        logger.info(f"Found {len(song_list)} songs to process")

        # Initialize statistics
        overall_stats = {
            "total_songs": len(song_list),
            "processed_songs": 0,
            "failed_songs": 0,
            "total_candidates": 0,
            "successful_extractions": 0,
            "processing_time": 0.0,
            "song_results": []
        }

        start_time = time.time()

        # Process songs sequentially (no threading to avoid hangs)
        for i, song_name in enumerate(song_list):
            logger.info(f"Processing song {i+1}/{len(song_list)}: {song_name}")

            try:
                # Process single song
                result = process_single_song(song_name, input_paths, output_path, extractor)

                if "error" in result:
                    logger.error(f"Failed to process {song_name}: {result['error']}")
                    overall_stats["failed_songs"] += 1
                else:
                    logger.info(f"Processed {song_name}: {result.get('successful_extractions', 0)} features")
                    overall_stats["processed_songs"] += 1
                    overall_stats["total_candidates"] += result.get("total_candidates", 0)
                    overall_stats["successful_extractions"] += result.get("successful_extractions", 0)

                overall_stats["song_results"].append(result)

                # Memory cleanup after each song
                gc.collect()

            except Exception as e:
                logger.error(f"Error processing {song_name}: {e}")
                overall_stats["failed_songs"] += 1
                overall_stats["song_results"].append({
                    "song": song_name,
                    "error": str(e)
                })

        # Calculate final statistics
        total_time = time.time() - start_time
        overall_stats["processing_time"] = total_time
        overall_stats["average_time_per_song"] = total_time / len(song_list) if song_list else 0.0
        overall_stats["overall_success_rate"] = (overall_stats["successful_extractions"] /
                                               overall_stats["total_candidates"]) if overall_stats["total_candidates"] > 0 else 0.0

        # Save overall statistics
        with open(output_path / "extraction_summary.json", 'w') as f:
            json.dump(overall_stats, f, indent=2, default=_json_serializer)

        # Log final results
        logger.info("Phase 6.5 Advanced Feature Extraction Complete!")
        logger.info(f"Processed {overall_stats['processed_songs']}/{overall_stats['total_songs']} songs")
        logger.info(f"Success rate: {overall_stats['overall_success_rate']:.2%}")
        logger.info(f"Total time: {total_time:.2f}s")
        logger.info(f"Results saved to: {output_path}")

        return overall_stats

    except Exception as e:
        logger.error(f"Phase 6.5 processing failed: {e}")
        logger.error(traceback.format_exc())
        return {"error": str(e)}


def process_single_song(song_name: str, input_paths: Dict[str, Path],
                       output_path: Path, extractor: AdvancedFeatureExtractor) -> Dict:
    """Process a single song."""
    song_start_time = time.time()

    try:
        # Load candidates
        candidates_file = input_paths["phase6"] / "note_candidates" / f"{song_name}_candidates.json"
        if not candidates_file.exists():
            return {
                "error": f"Candidates file not found: {candidates_file}",
                "song": song_name,
                "error_type": "missing_candidates_file"
            }

        try:
            with open(candidates_file, 'r') as f:
                candidates = json.load(f)
        except Exception as e:
            return {
                "error": f"Failed to load candidates file: {e}",
                "song": song_name,
                "error_type": "candidates_file_corrupt"
            }

        if not candidates:
            return {
                "error": "No candidates found in file",
                "song": song_name,
                "error_type": "empty_candidates"
            }

        # Load audio data
        audio_file = input_paths["phase1"] / "audio" / f"{song_name}.npy"
        if not audio_file.exists():
            return {
                "error": f"Audio file not found: {audio_file}",
                "song": song_name,
                "error_type": "missing_audio_file"
            }

        try:
            audio_data = np.load(audio_file)
            if len(audio_data) == 0:
                return {
                    "error": "Audio file is empty",
                    "song": song_name,
                    "error_type": "empty_audio_file"
                }
        except Exception as e:
            return {
                "error": f"Failed to load audio file: {e}",
                "song": song_name,
                "error_type": "audio_file_corrupt"
            }

        # Extract features
        features = extractor.extract_features(candidates, audio_data)

        # Save results
        save_song_results(song_name, features, output_path)

        # Calculate statistics
        processing_time = time.time() - song_start_time

        return {
            "song": song_name,
            "total_candidates": len(candidates),
            "successful_extractions": len(features),
            "extraction_success_rate": len(features) / len(candidates) if candidates else 0.0,
            "processing_time": processing_time
        }

    except Exception as e:
        logger.error(f"Error processing song {song_name}: {e}")
        return {"error": str(e), "song": song_name}


def save_song_results(song_name: str, features: List[Dict], output_path: Path):
    """Save song processing results."""
    try:
        # Save features JSON
        features_file = output_path / "advanced_features" / f"{song_name}_features.json"
        with open(features_file, 'w') as f:
            json.dump(features, f, indent=2, default=_json_serializer)

        # Save feature matrix
        if features:
            feature_matrix = np.array([f['feature_vector'] for f in features])
            matrix_file = output_path / "feature_matrices" / f"{song_name}_features.npy"
            np.save(matrix_file, feature_matrix)

    except Exception as e:
        logger.error(f"Error saving results for {song_name}: {e}")


def _json_serializer(obj):
    """JSON serializer for numpy arrays."""
    if isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    else:
        return str(obj)


# Example usage
if __name__ == "__main__":
    # Test with a single song
    test_songs = ["!!!Chaos Time!!!"]

    results = run_phase_06_5(
        song_list=test_songs,
        output_dir="data/test/phase6_5"
    )

    print(f"Results: {results}")
