#!/usr/bin/env python3
"""
Test script for enhanced Phase 6.5 feature extraction.
Validates the quality and completeness of the optimized features.
"""

import sys
import logging
import json
import numpy as np
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent.parent / "src"))

from phases.phase_06_5_feature_extraction import run_phase_06_5


def setup_logging():
    """Setup logging."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def analyze_feature_quality(features_file: Path):
    """Analyze the quality of extracted features."""
    with open(features_file, 'r') as f:
        features = json.load(f)
    
    if not features:
        print("No features found!")
        return False
    
    print(f"\n=== Feature Quality Analysis ===")
    print(f"Total candidates processed: {len(features)}")
    
    # Analyze first feature for structure
    first_feature = features[0]
    print(f"\nFeature structure:")
    for key in first_feature.keys():
        if key == 'feature_vector':
            print(f"  {key}: {len(first_feature[key])} dimensions")
        else:
            print(f"  {key}: {type(first_feature[key])}")
    
    # Validate feature vector dimensions
    feature_vectors = [f['feature_vector'] for f in features]
    vector_lengths = [len(fv) for fv in feature_vectors]
    
    print(f"\nFeature Vector Analysis:")
    print(f"  Expected dimensions: 128")
    print(f"  Actual dimensions: {vector_lengths[0]} (all vectors)")
    print(f"  All vectors same length: {len(set(vector_lengths)) == 1}")
    
    # Analyze feature content quality
    print(f"\nFeature Content Quality:")
    
    # Check for non-zero features
    non_zero_counts = []
    for fv in feature_vectors:
        non_zero_counts.append(np.count_nonzero(fv))
    
    print(f"  Average non-zero features per vector: {np.mean(non_zero_counts):.1f}")
    print(f"  Min non-zero features: {np.min(non_zero_counts)}")
    print(f"  Max non-zero features: {np.max(non_zero_counts)}")
    
    # Check feature ranges
    all_features = np.array(feature_vectors)
    print(f"  Feature value range: [{np.min(all_features):.3f}, {np.max(all_features):.3f}]")
    print(f"  Feature mean: {np.mean(all_features):.3f}")
    print(f"  Feature std: {np.std(all_features):.3f}")
    
    # Analyze specific feature categories
    print(f"\nSpecific Feature Analysis:")
    
    # MFCC features (first 13 dimensions)
    mfcc_features = all_features[:, :13]
    print(f"  MFCC features (dims 0-12):")
    print(f"    Non-zero ratio: {np.count_nonzero(mfcc_features) / mfcc_features.size:.3f}")
    print(f"    Range: [{np.min(mfcc_features):.3f}, {np.max(mfcc_features):.3f}]")
    
    # Spectral features (dims 13-20)
    spectral_features = all_features[:, 13:21]
    print(f"  Spectral features (dims 13-20):")
    print(f"    Non-zero ratio: {np.count_nonzero(spectral_features) / spectral_features.size:.3f}")
    print(f"    Range: [{np.min(spectral_features):.3f}, {np.max(spectral_features):.3f}]")
    
    # Chromatic features (dims 21-32)
    chromatic_features = all_features[:, 21:33]
    print(f"  Chromatic features (dims 21-32):")
    print(f"    Non-zero ratio: {np.count_nonzero(chromatic_features) / chromatic_features.size:.3f}")
    print(f"    Range: [{np.min(chromatic_features):.3f}, {np.max(chromatic_features):.3f}]")
    
    # Quality metrics analysis
    print(f"\nQuality Metrics Analysis:")
    quality_scores = [f['quality_metrics']['overall_feature_quality'] for f in features]
    high_quality_count = sum(1 for f in features if f['quality_metrics']['is_high_quality'])
    
    print(f"  Average quality score: {np.mean(quality_scores):.3f}")
    print(f"  High quality candidates: {high_quality_count}/{len(features)} ({high_quality_count/len(features)*100:.1f}%)")
    
    # Check for Phase 6 data utilization
    phase6_quality = [f['quality_metrics']['phase6_data_quality'] for f in features]
    print(f"  Phase 6 data utilization: {np.mean(phase6_quality):.3f}")
    
    return True


def main():
    """Run enhanced feature extraction test."""
    setup_logging()
    
    logger = logging.getLogger(__name__)
    logger.info("Testing Enhanced Phase 6.5 Feature Extraction")
    
    # Test with a single song first
    test_songs = ["!!!Chaos Time!!!"]
    test_output_dir = "data/test/enhanced_features"
    
    print(f"Testing enhanced features with: {test_songs}")
    print(f"Output directory: {test_output_dir}")
    
    try:
        # Run enhanced feature extraction
        results = run_phase_06_5(
            song_list=test_songs,
            output_dir=test_output_dir
        )
        
        if "error" in results:
            print(f"Feature extraction failed: {results['error']}")
            return 1
        
        print(f"\n=== Processing Results ===")
        print(f"Processed songs: {results.get('processed_songs', 0)}")
        print(f"Success rate: {results.get('overall_success_rate', 0):.2%}")
        print(f"Total candidates: {results.get('total_candidates', 0)}")
        print(f"Successful extractions: {results.get('successful_extractions', 0)}")
        
        # Analyze feature quality
        features_file = Path(test_output_dir) / "advanced_features" / f"{test_songs[0]}_features.json"
        
        if features_file.exists():
            success = analyze_feature_quality(features_file)
            if success:
                print(f"\n✅ Enhanced feature extraction test completed successfully!")
                return 0
            else:
                print(f"\n❌ Feature quality analysis failed!")
                return 1
        else:
            print(f"\n❌ Features file not found: {features_file}")
            return 1
        
    except Exception as e:
        print(f"\n❌ Test failed with exception: {e}")
        logger.error("Full traceback:", exc_info=True)
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
